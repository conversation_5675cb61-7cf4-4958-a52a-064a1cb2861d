@extends('layouts.app')

@section('content')
<main class="pt-90">
    <div class="container">
        <h2 class="page-title mb-4">My Addresses</h2>

        <div class="row gy-4">
            <!-- Sidebar -->
            <div class="col-12 col-lg-3">
                @include('user.account-nav')
            </div>

            <!-- Main Content -->
            <div class="col-12 col-lg-9">
                @forelse ($addresses as $address)
                    <div class="card shadow-sm mb-4 {{ $address->isdefault ? 'border-primary' : '' }}">
                        <div class="card-body">
                            <h5 class="card-title mb-2">{{ $address->name }}</h5>

                            <p class="mb-1"><strong>Phone:</strong> {{ $address->phone }}</p>
                            <p class="mb-1"><strong>Address:</strong> 
                                {{ $address->address }}, {{ $address->locality }},
                                {{ $address->city }}, {{ $address->state }},
                                {{ $address->zip }}
                            </p>
                            <p class="mb-2"><strong>Country:</strong> {{ $address->country }}</p>

                            @if($address->isdefault)
                                <span class="badge bg-primary">Default</span>
                            @endif

                            <div class="d-flex justify-content-end mt-3">
                                <a href="{{ route('user.addresses.edit', $address->id) }}"
                                   class="btn btn-sm btn-outline-secondary me-2">
                                    Edit
                                </a>

                                <form method="POST"
                                      action="{{ route('user.addresses.destroy', $address->id) }}"
                                      onsubmit="return confirm('Yakin ingin menghapus alamat ini?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        Hapus
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="alert alert-info">
                        Belum ada alamat yang ditambahkan.
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</main>
@endsection