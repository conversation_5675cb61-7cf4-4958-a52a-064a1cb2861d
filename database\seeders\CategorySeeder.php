<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            ['name' => 'Tanaman Indoor', 'parent_id' => null],
            ['name' => 'Tanaman Outdoor', 'parent_id' => null],
            ['name' => 'Tanaman Gantung', 'parent_id' => null],
            ['name' => 'Tanaman Hias Daun', 'parent_id' => null],
            ['name' => 'Kaktus & Sukulen', 'parent_id' => null],
            ['name' => 'Tanaman Air', 'parent_id' => null],
            ['name' => 'Tanaman Buah Mini', 'parent_id' => null],
            ['name' => 'Pot Tanaman', 'parent_id' => null],
            ['name' => 'Pupuk & Media Tanam', 'parent_id' => null],
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'name' => $category['name'],
                'slug' => Str::slug($category['name']),
                'image' => null,
                'parent_id' => $category['parent_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}