<style>
    /* Hover style untuk link navigasi */
    .nav-link:hover {
        background-color: #f8f9fa;
    }

    /* Style untuk link aktif */
    .nav-link.active {
        color: #fff !important;
        background-color: #0d6efd !important; /* Tambahan untuk highlight aktif */
    }
</style>

<div class="d-flex flex-column flex-shrink-0 p-3 bg-light rounded shadow-sm" style="min-height: 100%; min-width: 200px;">
    @php
        $navItems = [
            ['route' => 'user.index', 'icon' => 'bi-house', 'label' => 'Pengaturan Akun'],
            ['route' => 'user.orders', 'icon' => 'bi-bag', 'label' => 'Orders'],
            ['route' => 'user.addresses', 'icon' => 'bi-geo-alt', 'label' => 'Addresses'],
            ['route' => 'wishlist.index', 'icon' => 'bi-heart', 'label' => 'Wishlist'],
        ];
    @endphp

    <ul class="nav nav-pills flex-column mb-auto account-nav">
        @foreach ($navItems as $item)
            <li class="nav-item">
                <a href="{{ route($item['route']) }}"
                   class="menu-link menu-link_us-s nav-link {{ request()->routeIs($item['route']) ? 'active' : '' }}">
                    <i class="bi {{ $item['icon'] }} me-2"></i> {{ $item['label'] }}
                </a>
            </li>
        @endforeach

        <li class="nav-item">
            <form method="POST" action="{{ route('logout') }}" id="logout-form">
                @csrf
                <a href="{{ route('logout') }}"
                   class="menu-link menu-link_us-s nav-link"
                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                    <i class="bi bi-box-arrow-right me-2"></i> Logout
                </a>
            </form>
        </li>
    </ul>
</div>