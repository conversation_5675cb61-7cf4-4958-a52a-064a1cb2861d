@extends('layouts.admin')
@section('content')
    <div class="main-content-inner">
        <div class="main-content-wrap">
            <div class="flex items-center flex-wrap justify-between gap20 mb-27">
                <h3>Laporan Transaksi</h3>
                <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
                    <li>
                        <a href="{{ route('admin.index') }}">
                            <div class="text-tiny">Dashboard</div>
                        </a>
                    </li>
                    <li><i class="icon-chevron-right"></i></li>
                    <li>
                        <div class="text-tiny">Laporan Transaksi</div>
                    </li>
                </ul>
            </div>

            <div class="wg-box">
                <form method="GET" action="{{ route('admin.laporan.index') }}" class="mb-4">
                    <div class="flex items-center justify-between gap10 flex-wrap">
                        <div class="wg-filter flex-grow">
                            <form class="form-search"action="{{ route('admin.laporan.index') }}" class="mb-4">
                                <div>
                                    <label for="start_date" class="form-label"><PERSON><PERSON></label>
                                    <input type="date" name="start_date" id="start_date" class="form-control"
                                        value="{{ request('start_date') }}">
                                </div>
                            </form>
                        </div>
                        <a class="tf-button style-1 w208" href="{{ route('admin.category.add') }}">Filter</a>
                        <a href="{{ route('admin.laporan.exportPdf', request()->all()) }}"
                            class="tf-button style-3 w208">Export PDF</a>
                    </div>


                    <div class="grid grid-cols-1 md:grid-cols-4 gap20">
                        <div>
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" name="start_date" id="start_date" class="form-control"
                                value="{{ request('start_date') }}">
                        </div>
                        <div>
                            <label for="end_date" class="form-label">Tanggal Selesai</label>
                            <input type="date" name="end_date" id="end_date" class="form-control"
                                value="{{ request('end_date') }}">
                        </div>
                        <div>
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">Semua</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending
                                </option>
                                <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>
                                    Processing</option>
                                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed
                                </option>
                                <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>
                                    Cancelled</option>
                            </select>
                        </div>
                        <div class="flex gap10 items-end">
                            <button type="submit" class="btn btn-primary w-full">Filter</button>
                        </div>
                    </div>

                    
                </form>

                <div class="wg-table">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr class="text-center">
                                    <th>No</th>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Tanggal</th>
                                    <th>Metode</th>
                                    <th>Status</th>
                                    <th>Payment Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($transactions as $t)
                                    <tr>
                                        <td class="text-center">{{ $loop->iteration }}</td>
                                        <td class="text-center">{{ $t->order->order_id }}</td>
                                        <td class="text-center">{{ $t->address->name ?? '-' }}</td>
                                        <td class="text-center">{{ $t->created_at->format('d-m-Y') }}</td>
                                        <td class="text-uppercase text-center">{{ $t->mode }}</td>
                                        <td class="text-center">
                                            @switch($t->status)
                                                @case('pending')
                                                    <span class="badge bg-warning">Pending</span>
                                                @break

                                                @case('processing')
                                                    <span class="badge bg-primary">Processing</span>
                                                @break

                                                @case('completed')
                                                    <span class="badge bg-success">Completed</span>
                                                @break

                                                @case('cancelled')
                                                    <span class="badge bg-danger">Cancelled</span>
                                                @break

                                                @default
                                                    <span class="badge bg-secondary">Unknown</span>
                                            @endswitch
                                        </td>
                                        <td class="text-capitalize text-center">{{ $t->payment_status }}</td>
                                    </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">Tidak ada data.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="divider"></div>
                    <div class="flex items-center justify-between flex-wrap gap10 wgp-pagination">
                        {{-- {{ $transactions->links('pagination::bootstrap-5') }} --}}
                    </div>
                </div>
            </div>
        </div>
    @endsection
