@extends('layouts.app')

@section('content')
<main class="pt-90">
    <section class="my-account container">
        <h2 class="page-title mb-4">Wishlist</h2>

        <div class="row gy-4">
            <!-- Sidebar -->
            <div class="col-12 col-lg-3">
                @include('user.account-nav')
            </div>

            <!-- Wishlist Content -->
            <div class="col-12 col-lg-9">
                @if(Cart::instance('wishlist')->content()->count() > 0)
                    <div class="card shadow-sm border-0">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table align-middle">
                                    <thead>
                                        <tr>
                                            <th>Product</th>
                                            <th>Name</th>
                                            <th>Price</th>
                                            <th>Qty</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($items as $item)
                                            <tr>
                                                <td>
                                                    <img src="{{ asset('uploads/products/thumbnails/' . $item->model->image) }}"
                                                         alt="{{ $item->name }}"
                                                         width="80" height="80" class="img-thumbnail">
                                                </td>
                                                <td>
                                                    <strong>{{ $item->name }}</strong>
                                                </td>
                                                <td>Rp{{ number_format($item->price, 0, ',', '.') }}</td>
                                                <td>{{ $item->qty }}</td>
                                                <td>
                                                    <div class="d-flex flex-wrap gap-2">
                                                        <form method="POST" action="{{ route('wishlist.move.to.cart', ['rowId' => $item->rowId]) }}">
                                                            @csrf
                                                            <button type="submit" class="btn btn-sm btn-warning">Pindah ke Keranjang</button>
                                                        </form>

                                                        <form method="POST" action="{{ route('wishlist.item.remove', ['rowId' => $item->rowId]) }}" id="remove-item-{{ $item->id }}">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="document.getElementById('remove-item-{{ $item->id }}').submit();">
                                                                Hapus
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3">
                                <form method="POST" action="{{ route('wishlist.item.clear') }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-light">Kosongkan Wishlist</button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="alert alert-info">
                        Wishlist kamu kosong.
                    </div>
                    <a href="{{ route('shop.index') }}" class="btn btn-primary mt-2">Lihat Produk</a>
                @endif
            </div>
        </div>
    </section>
</main>
@endsection